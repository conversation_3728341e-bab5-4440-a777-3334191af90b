2025-03-29 13:25:44,627 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-03-29 13:25:44,664 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-03-29 13:25:44,668 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-03-29 13:25:44,671 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-03-29 13:25:44,673 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-03-29 13:25:44,689 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-03-29 13:25:44,691 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-03-29 13:25:44,696 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-03-29 13:25:44,699 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-03-29 13:25:44,701 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-03-29 13:25:44,703 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-03-29 13:25:44,767 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-03-29 13:25:44,769 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-03-29 13:25:44,771 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-03-29 13:25:44,774 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-03-29 18:01:38,950 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for explore
2025-03-30 10:37:19,268 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-03-30 10:37:19,302 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-03-30 10:37:19,306 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-03-30 10:37:19,308 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-03-30 10:37:19,310 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-03-30 10:37:19,321 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-03-30 10:37:19,323 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-03-30 10:37:19,326 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-03-30 10:37:19,328 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-03-30 10:37:19,330 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-03-30 10:37:19,332 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-03-30 10:37:19,384 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for explore
2025-03-30 10:37:19,386 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for explore
2025-03-30 10:37:19,389 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for explore
2025-03-30 10:37:19,391 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for explore
2025-03-30 10:37:19,393 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for explore
2025-03-30 10:37:19,394 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for explore
2025-03-30 10:37:19,396 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-03-30 10:37:19,398 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-03-30 10:37:19,400 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-03-30 10:37:19,402 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-03-31 11:24:00,731 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-03-31 11:24:00,735 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-03-31 11:24:00,789 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-03-31 11:24:00,794 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-03-31 11:24:00,796 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-03-31 11:24:00,799 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-03-31 11:24:00,816 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-03-31 11:24:00,818 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-03-31 11:24:00,823 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-03-31 11:24:00,826 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-03-31 11:24:00,830 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-03-31 11:24:00,833 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-03-31 11:24:00,947 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-03-31 11:24:00,950 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-03-31 11:24:00,954 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-03-31 11:24:00,957 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-13 14:26:22,701 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-13 14:26:22,710 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-13 14:26:22,712 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-13 14:26:22,716 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-13 14:26:22,722 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-13 14:26:22,724 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-13 14:26:22,731 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-13 14:26:22,742 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-13 14:26:22,754 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for explore
2025-04-13 14:26:22,755 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-13 14:26:22,761 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for explore
2025-04-13 14:26:22,766 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for explore
2025-04-13 14:26:22,771 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-13 14:26:22,772 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for explore
2025-04-13 14:26:22,780 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-13 14:26:22,782 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-23 12:00:41,736 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-23 12:00:41,746 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-23 12:00:41,750 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-23 12:00:41,755 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-23 12:00:41,762 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-23 12:00:41,769 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-23 12:00:41,772 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-23 12:00:41,774 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-23 12:00:41,775 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-23 12:00:41,782 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-23 12:00:41,787 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-23 12:00:41,788 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-23 12:00:41,804 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-23 12:00:41,811 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-23 12:00:41,813 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-23 12:00:41,823 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-24 08:34:05,874 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-24 08:34:05,877 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-24 08:34:05,882 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-04-24 08:34:05,886 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-24 08:34:05,888 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for explore
2025-04-24 08:34:05,891 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for explore
2025-04-24 08:34:05,893 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for explore
2025-04-24 08:34:05,894 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for explore
2025-04-24 08:34:05,898 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-04-24 08:34:05,899 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-24 08:34:05,902 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for explore
2025-04-24 08:34:05,903 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for explore
2025-04-24 08:34:05,906 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-24 08:34:05,908 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for explore
2025-04-24 08:34:05,910 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-04-24 08:34:05,913 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for explore
2025-04-24 08:34:05,914 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for explore
2025-04-24 08:34:05,917 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for explore
2025-04-24 08:34:05,919 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for explore
2025-04-24 08:34:05,920 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for explore
2025-04-24 08:34:05,921 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-24 08:34:05,923 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for explore
2025-04-24 08:34:05,926 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for explore
2025-04-24 08:34:05,929 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-04-24 08:34:05,932 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-24 08:34:05,934 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for explore
2025-04-24 08:34:05,937 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for explore
2025-04-24 08:34:05,941 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for explore
2025-04-24 08:34:05,943 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-24 08:34:05,946 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for explore
2025-04-24 08:34:05,947 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-24 08:34:05,948 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for explore
2025-04-24 08:34:05,951 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-04-24 08:34:05,952 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-24 08:34:05,953 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for explore
2025-04-24 08:34:05,955 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for explore
2025-04-24 08:34:05,958 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for explore
2025-04-24 08:34:05,962 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-24 08:34:05,963 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-24 08:34:05,965 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-24 08:34:05,966 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for explore
2025-04-24 08:34:05,970 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-24 08:34:05,975 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-24 08:34:05,977 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-24 08:34:05,980 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-24 10:21:02,544 ERROR scheduler Exception in Enqueue Events for Site explore
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_735b0b6f43cd3001'@'localhost' (using password: YES)")
2025-04-24 15:56:57,160 ERROR scheduler Exception in Enqueue Events for Site explore
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
                  ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1045, "Access denied for user '_735b0b6f43cd3001'@'localhost' (using password: YES)")
2025-04-25 08:26:09,085 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-04-25 08:26:09,089 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for explore
2025-04-25 08:26:09,092 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for explore
2025-04-25 08:26:09,095 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-04-25 08:26:09,097 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for explore
2025-04-25 08:26:09,099 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for explore
2025-04-25 08:26:09,101 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for explore
2025-04-25 08:26:09,104 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for explore
2025-04-25 08:26:09,108 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-04-25 08:26:09,112 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for explore
2025-04-25 08:26:09,118 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for explore
2025-04-25 08:26:09,122 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for explore
2025-04-25 08:26:09,125 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for explore
2025-04-25 08:26:09,128 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-04-25 08:26:09,131 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for explore
2025-04-25 08:26:09,134 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for explore
2025-04-25 08:26:09,136 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for explore
2025-04-25 08:26:09,152 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-25 08:26:09,154 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for explore
2025-04-25 08:26:09,158 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for explore
2025-04-25 08:26:09,161 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-25 08:26:09,166 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-25 08:26:09,168 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-25 08:26:09,171 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for explore
2025-04-25 08:26:09,173 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for explore
2025-04-25 08:26:09,176 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for explore
2025-04-25 08:26:09,181 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for explore
2025-04-25 08:26:09,183 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for explore
2025-04-25 08:26:09,185 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-25 08:26:09,188 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for explore
2025-04-25 08:26:09,191 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for explore
2025-04-25 08:26:09,196 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-25 08:26:09,199 ERROR scheduler Skipped queueing icd_tz.icd_tz.doctype.consignee.consignee.create_customer because it was found in queue for explore
2025-04-25 08:26:09,201 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-04-25 08:26:09,203 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for explore
2025-04-25 08:26:09,205 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-25 08:26:09,210 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for explore
2025-04-25 08:26:09,212 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-04-25 08:26:09,217 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for explore
2025-04-25 08:26:09,225 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-25 08:26:09,226 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-25 08:26:09,228 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for explore
2025-04-25 08:26:09,231 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for explore
2025-04-25 08:26:09,235 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-25 08:26:09,238 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-04-25 08:26:09,241 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-25 08:26:09,244 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-25 08:26:09,246 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-25 08:26:09,252 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for explore
2025-04-25 08:26:09,256 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-04-25 08:26:09,259 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-25 08:26:09,262 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for explore
2025-04-25 08:26:09,265 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for explore
2025-04-25 08:26:09,268 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for explore
2025-04-25 08:26:09,270 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-25 08:26:09,275 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for explore
2025-04-25 08:26:09,278 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-04-25 08:26:09,280 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for explore
2025-04-25 08:26:09,281 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for explore
2025-04-25 08:26:09,284 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-25 08:26:09,289 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for explore
2025-04-25 08:26:09,291 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for explore
2025-04-25 08:26:09,294 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for explore
2025-04-25 08:26:09,298 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for explore
2025-04-25 08:26:09,302 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for explore
2025-04-25 08:26:09,310 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-26 11:53:53,742 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-26 11:53:53,746 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-26 11:53:53,756 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-26 11:53:53,758 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-26 11:53:53,771 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-26 11:53:53,781 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-26 11:53:53,789 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-26 11:53:53,802 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-26 11:53:53,813 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-26 11:53:53,815 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-26 11:53:53,816 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-26 11:53:53,821 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-26 11:53:53,826 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-26 11:53:53,828 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-26 11:53:53,830 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-26 11:53:53,844 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-26 11:53:53,847 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-27 11:13:56,413 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for explore
2025-04-27 11:13:56,417 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-04-27 11:13:56,419 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for explore
2025-04-27 11:13:56,420 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for explore
2025-04-27 11:13:56,424 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for explore
2025-04-27 11:13:56,426 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for explore
2025-04-27 11:13:56,427 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for explore
2025-04-27 11:13:56,428 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-04-27 11:13:56,430 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for explore
2025-04-27 11:13:56,431 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for explore
2025-04-27 11:13:56,433 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-27 11:13:56,435 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for explore
2025-04-27 11:13:56,436 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for explore
2025-04-27 11:13:56,437 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for explore
2025-04-27 11:13:56,439 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-27 11:13:56,440 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for explore
2025-04-27 11:13:56,441 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for explore
2025-04-27 11:13:56,444 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-27 11:13:56,445 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for explore
2025-04-27 11:13:56,448 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-27 11:13:56,449 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for explore
2025-04-27 11:13:56,451 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-04-27 11:13:56,453 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for explore
2025-04-27 11:13:56,455 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for explore
2025-04-27 11:13:56,459 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-27 11:13:56,460 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-04-27 11:13:56,461 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-27 11:13:56,462 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_weekly because it was found in queue for explore
2025-04-27 11:13:56,463 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for explore
2025-04-27 11:13:56,465 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for explore
2025-04-27 11:13:56,468 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for explore
2025-04-27 11:13:56,469 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for explore
2025-04-27 11:13:56,470 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for explore
2025-04-27 11:13:56,471 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-27 11:13:56,475 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-04-27 11:13:56,476 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for explore
2025-04-27 11:13:56,478 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-27 11:13:56,480 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-27 11:13:56,482 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for explore
2025-04-27 11:13:56,483 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for explore
2025-04-27 11:13:56,484 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-27 11:13:56,485 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-27 11:13:56,487 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for explore
2025-04-27 11:13:56,490 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for explore
2025-04-27 11:13:56,491 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for explore
2025-04-27 11:13:56,492 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-27 11:13:56,493 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for explore
2025-04-27 11:13:56,494 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-04-27 11:13:56,496 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-27 11:13:56,498 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for explore
2025-04-27 11:13:56,500 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-27 11:13:56,501 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-27 11:13:56,503 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for explore
2025-04-27 11:13:56,504 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for explore
2025-04-27 11:13:56,506 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for explore
2025-04-27 11:13:56,507 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for explore
2025-04-27 11:13:56,509 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for explore
2025-04-27 11:13:56,510 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for explore
2025-04-27 11:13:56,512 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-04-27 11:13:56,516 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for explore
2025-04-27 11:13:56,517 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for explore
2025-04-27 11:13:56,519 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for explore
2025-04-27 11:13:56,521 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for explore
2025-04-27 11:13:56,522 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for explore
2025-04-27 11:13:56,523 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-27 11:13:56,525 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for explore
2025-04-27 11:13:56,526 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for explore
2025-04-27 11:13:56,528 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for explore
2025-04-27 11:13:56,529 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-27 11:13:56,532 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for explore
2025-04-27 11:13:56,533 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-04-27 11:13:56,534 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for explore
2025-04-27 11:13:56,535 ERROR scheduler Skipped queueing hrms.hr.doctype.job_opening.job_opening.close_expired_job_openings because it was found in queue for explore
2025-04-28 08:41:20,370 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-28 08:41:20,374 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for explore
2025-04-28 08:41:20,377 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for explore
2025-04-28 08:41:20,380 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for explore
2025-04-28 08:41:20,383 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for explore
2025-04-28 08:41:20,385 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for explore
2025-04-28 08:41:20,388 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for explore
2025-04-28 08:41:20,390 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for explore
2025-04-28 08:41:20,393 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for explore
2025-04-28 08:41:20,395 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for explore
2025-04-28 08:41:20,397 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-28 08:41:20,399 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-04-28 08:41:20,402 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for explore
2025-04-28 08:41:20,403 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for explore
2025-04-28 08:41:20,406 ERROR scheduler Skipped queueing erpnext.stock.reorder_item.reorder_item because it was found in queue for explore
2025-04-28 08:41:20,407 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-04-28 08:41:20,408 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for explore
2025-04-28 08:41:20,410 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-04-28 08:41:20,411 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for explore
2025-04-28 08:41:20,412 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-28 08:41:20,414 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-04-28 08:41:20,415 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-04-28 08:41:20,417 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-28 08:41:20,418 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-28 08:41:20,420 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-04-28 08:41:20,421 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for explore
2025-04-28 08:41:20,422 ERROR scheduler Skipped queueing erpnext.quality_management.doctype.quality_review.quality_review.review because it was found in queue for explore
2025-04-28 08:41:20,424 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for explore
2025-04-28 08:41:20,425 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-28 08:41:20,427 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for explore
2025-04-28 08:41:20,428 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-28 08:41:20,429 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for explore
2025-04-28 08:41:20,430 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for explore
2025-04-28 08:41:20,431 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-28 08:41:20,432 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-28 08:41:20,433 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for explore
2025-04-28 08:41:20,435 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-28 08:41:20,437 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for explore
2025-04-28 08:41:20,438 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for explore
2025-04-28 08:41:20,440 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for explore
2025-04-28 08:41:20,442 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for explore
2025-04-28 08:41:20,445 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for explore
2025-04-28 08:41:20,446 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for explore
2025-04-28 08:41:20,449 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-28 08:41:20,450 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for explore
2025-04-28 08:41:20,451 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for explore
2025-04-28 08:41:20,453 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for explore
2025-04-28 08:41:20,454 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-04-28 08:41:20,455 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-28 08:41:20,459 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.set_email_campaign_status because it was found in queue for explore
2025-04-28 08:41:20,461 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for explore
2025-04-28 08:41:20,463 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for explore
2025-04-28 08:41:20,464 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-28 08:41:20,466 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-28 08:41:20,468 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for explore
2025-04-28 08:41:20,469 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-28 08:41:20,471 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for explore
2025-04-28 08:41:20,472 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-28 08:41:20,474 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for explore
2025-04-28 08:41:20,475 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for explore
2025-04-28 08:41:20,476 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-04-28 08:41:20,478 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_daily_feedback_reminder because it was found in queue for explore
2025-04-28 08:41:20,479 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for explore
2025-04-28 08:41:20,480 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-04-28 08:41:20,482 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for explore
2025-04-28 08:41:20,483 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for explore
2025-04-28 08:41:20,485 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for explore
2025-04-28 08:41:20,486 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for explore
2025-04-28 08:41:20,488 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for explore
2025-04-28 08:41:20,490 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for explore
2025-04-28 08:41:20,491 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-28 08:41:20,492 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for explore
2025-04-28 08:41:20,494 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for explore
2025-04-28 08:41:20,495 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for explore
2025-04-28 08:41:20,496 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-04-29 08:54:59,217 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-29 08:54:59,220 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-29 08:54:59,224 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-29 08:54:59,227 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-29 08:54:59,232 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-29 08:54:59,233 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-29 08:54:59,235 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-29 08:54:59,236 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-29 08:54:59,243 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-29 08:54:59,248 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-29 08:54:59,250 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-29 08:54:59,256 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-04-29 08:54:59,266 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-29 08:54:59,270 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-29 08:54:59,282 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-29 08:54:59,284 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-29 08:54:59,303 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-30 08:57:44,865 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-04-30 08:57:44,870 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for explore
2025-04-30 08:57:44,875 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-04-30 08:57:44,884 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-04-30 08:57:44,889 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-04-30 08:57:44,894 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-04-30 08:57:44,896 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-04-30 08:57:44,898 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-04-30 08:57:44,909 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for explore
2025-04-30 08:57:44,911 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-04-30 08:57:44,913 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-04-30 08:57:44,917 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for explore
2025-04-30 08:57:44,920 ERROR scheduler Skipped queueing erpnext.controllers.accounts_controller.update_invoice_status because it was found in queue for explore
2025-04-30 08:57:44,922 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-04-30 08:57:44,923 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-04-30 08:57:44,925 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-04-30 08:57:44,944 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-04-30 08:57:44,956 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.material_request.auto_close_material_request because it was found in queue for explore
2025-04-30 08:57:44,961 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-04-30 08:57:44,989 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-04-30 08:57:44,994 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-04-30 08:57:44,995 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-04-30 08:57:45,013 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for explore
2025-04-30 08:57:45,018 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-05-01 14:01:59,174 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for explore
2025-05-01 14:01:59,176 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for explore
2025-05-01 14:01:59,178 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for explore
2025-05-01 14:01:59,179 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-05-01 14:01:59,180 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for explore
2025-05-01 14:01:59,181 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for explore
2025-05-01 14:01:59,182 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-05-01 14:01:59,183 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for explore
2025-05-01 14:01:59,184 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-05-01 14:01:59,185 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for explore
2025-05-01 14:01:59,186 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-05-01 14:01:59,187 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for explore
2025-05-01 14:01:59,188 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for explore
2025-05-01 14:01:59,189 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-05-01 14:01:59,190 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for explore
2025-05-01 14:01:59,191 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for explore
2025-05-01 14:01:59,192 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for explore
2025-05-01 14:01:59,193 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for explore
2025-05-01 14:01:59,194 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for explore
2025-05-01 14:01:59,195 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for explore
2025-05-01 14:01:59,196 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for explore
2025-05-01 14:01:59,198 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for explore
2025-05-01 14:01:59,199 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-05-01 14:01:59,200 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-05-01 14:01:59,201 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for explore
2025-05-01 14:01:59,202 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for explore
2025-05-01 14:01:59,204 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for explore
2025-05-01 14:01:59,206 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for explore
2025-05-01 14:01:59,207 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for explore
2025-05-01 14:01:59,208 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-05-01 14:01:59,210 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for explore
2025-05-01 14:01:59,212 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for explore
2025-05-01 14:01:59,213 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-05-01 14:01:59,214 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-05-01 14:01:59,216 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for explore
2025-05-01 14:01:59,217 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for explore
2025-05-01 14:01:59,218 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for explore
2025-05-01 14:01:59,220 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for explore
2025-05-01 14:01:59,222 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-05-01 14:01:59,223 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-05-01 14:01:59,224 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for explore
2025-05-01 14:01:59,225 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-05-01 14:01:59,226 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for explore
2025-05-01 14:01:59,227 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for explore
2025-05-01 14:01:59,229 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-05-01 14:01:59,230 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.update_project_sales_billing because it was found in queue for explore
2025-05-01 14:01:59,231 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for explore
2025-05-01 14:01:59,233 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_monthly because it was found in queue for explore
2025-05-01 14:01:59,234 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for explore
2025-05-01 14:01:59,235 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-05-01 14:01:59,237 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for explore
2025-05-01 14:01:59,238 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for explore
2025-05-01 14:01:59,239 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for explore
2025-05-01 14:01:59,240 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for explore
2025-05-01 14:01:59,240 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for explore
2025-05-01 14:01:59,241 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for explore
2025-05-01 14:01:59,243 ERROR scheduler Skipped queueing erpnext.accounts.utils.run_ledger_health_checks because it was found in queue for explore
2025-05-01 14:01:59,244 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for explore
2025-05-01 14:01:59,246 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-05-01 14:01:59,247 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-05-01 14:01:59,248 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for explore
2025-05-01 14:01:59,249 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for explore
2025-05-01 14:01:59,250 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for explore
2025-05-01 14:01:59,251 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for explore
2025-05-01 14:01:59,253 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.send_project_status_email_to_users because it was found in queue for explore
2025-05-01 14:01:59,254 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-05-01 14:01:59,256 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for explore
2025-05-01 14:01:59,257 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-05-01 14:01:59,258 ERROR scheduler Skipped queueing icd_tz.icd_tz.doctype.container.container.daily_update_date_container_stay because it was found in queue for explore
2025-05-01 14:01:59,260 ERROR scheduler Skipped queueing erpnext.support.doctype.service_level_agreement.service_level_agreement.check_agreement_status because it was found in queue for explore
2025-05-01 14:01:59,261 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for explore
2025-05-01 14:01:59,263 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for explore
2025-05-01 14:01:59,265 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for explore
2025-05-01 14:01:59,266 ERROR scheduler Skipped queueing erpnext.accounts.deferred_revenue.process_deferred_accounting because it was found in queue for explore
2025-05-01 14:01:59,268 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for explore
2025-05-01 14:01:59,269 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for explore
2025-05-01 14:01:59,270 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for explore
2025-05-01 14:01:59,272 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_monthly because it was found in queue for explore
2025-05-01 14:01:59,273 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for explore
2025-05-01 14:01:59,274 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for explore
2025-05-01 14:01:59,275 ERROR scheduler Skipped queueing icd_tz.icd_tz.doctype.consignee.consignee.create_customer because it was found in queue for explore
2025-05-01 14:01:59,277 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for explore
2025-05-01 14:01:59,278 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for explore
2025-05-01 14:01:59,279 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for explore
2025-05-01 14:01:59,280 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for explore
2025-05-01 14:01:59,281 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for explore
2025-05-01 14:01:59,282 ERROR scheduler Skipped queueing erpnext.accounts.doctype.fiscal_year.fiscal_year.auto_create_fiscal_year because it was found in queue for explore
2025-05-01 14:01:59,283 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for explore
2025-05-01 14:01:59,284 ERROR scheduler Skipped queueing csf_tz.bank_api.reconciliation because it was found in queue for explore
2025-05-01 14:01:59,285 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-05-01 14:02:59,728 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_scorecard.supplier_scorecard.refresh_scorecards because it was found in queue for explore
2025-05-01 14:02:59,730 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for explore
2025-05-01 14:02:59,732 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for explore
2025-05-01 14:02:59,733 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for explore
2025-05-01 14:02:59,735 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for explore
2025-05-01 14:02:59,736 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for explore
2025-05-01 14:02:59,737 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_birthday_reminders because it was found in queue for explore
2025-05-01 14:02:59,738 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for explore
2025-05-01 14:02:59,739 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for explore
2025-05-01 14:02:59,740 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for explore
2025-05-01 14:02:59,743 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for explore
2025-05-01 14:02:59,744 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_work_anniversary_reminders because it was found in queue for explore
2025-05-01 14:02:59,746 ERROR scheduler Skipped queueing erpnext.accounts.deferred_revenue.process_deferred_accounting because it was found in queue for explore
2025-05-01 14:02:59,747 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for explore
2025-05-01 14:02:59,748 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for explore
2025-05-01 14:02:59,750 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_monthly because it was found in queue for explore
2025-05-01 14:02:59,751 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_statement_of_accounts.process_statement_of_accounts.send_auto_email because it was found in queue for explore
2025-05-01 14:02:59,752 ERROR scheduler Skipped queueing erpnext.projects.doctype.task.task.set_tasks_as_overdue because it was found in queue for explore
2025-05-01 14:02:59,755 ERROR scheduler Skipped queueing csf_tz.custom_api.create_delivery_note_for_all_pending_sales_invoice because it was found in queue for explore
2025-05-01 14:02:59,756 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for explore
2025-05-01 14:02:59,757 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for explore
2025-05-01 14:02:59,759 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for explore
2025-05-01 14:02:59,763 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for explore
2025-05-01 14:02:59,765 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for explore
2025-05-01 14:02:59,768 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.update_maintenance_status because it was found in queue for explore
2025-05-01 14:02:59,771 ERROR scheduler Skipped queueing erpnext.selling.doctype.quotation.quotation.set_expired_status because it was found in queue for explore
2025-05-01 14:02:59,773 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for explore
2025-05-01 14:02:59,774 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for explore
2025-05-01 14:02:59,775 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.additional_salary.generate_additional_salary_records because it was found in queue for explore
2025-05-01 14:02:59,777 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for explore
2025-05-01 14:02:59,778 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for explore
2025-05-01 14:02:59,780 ERROR scheduler Skipped queueing erpnext.crm.doctype.contract.contract.update_status_for_contracts because it was found in queue for explore
2025-05-01 14:02:59,782 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for explore
2025-05-01 14:02:59,783 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for explore
2025-05-01 14:02:59,785 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset_maintenance_log.asset_maintenance_log.update_asset_maintenance_log_status because it was found in queue for explore
2025-05-01 14:02:59,787 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for explore
2025-05-01 14:02:59,789 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for explore
2025-05-01 14:02:59,790 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.asset.make_post_gl_entry because it was found in queue for explore
2025-05-01 14:02:59,793 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for explore
2025-05-01 14:02:59,794 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for explore
2025-05-01 14:02:59,795 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for explore
2025-05-01 14:02:59,796 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for explore
2025-05-01 14:02:59,798 ERROR scheduler Skipped queueing erpnext.support.doctype.issue.issue.auto_close_tickets because it was found in queue for explore
2025-05-01 14:02:59,799 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for explore
2025-05-01 14:02:59,800 ERROR scheduler Skipped queueing erpnext.buying.doctype.supplier_quotation.supplier_quotation.set_expired_status because it was found in queue for explore
2025-05-01 14:02:59,802 ERROR scheduler Skipped queueing erpnext.crm.doctype.opportunity.opportunity.auto_close_opportunity because it was found in queue for explore
2025-05-01 14:02:59,804 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for explore
2025-05-01 14:02:59,805 ERROR scheduler Skipped queueing erpnext.stock.doctype.serial_no.serial_no.update_maintenance_status because it was found in queue for explore
2025-05-01 14:02:59,806 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for explore
2025-05-01 14:02:59,807 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for explore
2025-05-01 14:02:59,808 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_daily because it was found in queue for explore
2025-05-01 14:02:59,810 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for explore
2025-05-01 14:02:59,811 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for explore
2025-05-01 14:02:59,812 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for explore
2025-05-01 14:02:59,813 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for explore
2025-05-01 14:02:59,814 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for explore
2025-05-01 14:02:59,815 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.send_summary because it was found in queue for explore
2025-05-01 14:02:59,817 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for explore
2025-05-01 14:02:59,818 ERROR scheduler Skipped queueing erpnext.setup.doctype.company.company.cache_companies_monthly_sales_history because it was found in queue for explore
2025-05-01 14:02:59,819 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for explore
2025-05-01 14:02:59,821 ERROR scheduler Skipped queueing csf_tz.custom_api.auto_close_dn because it was found in queue for explore
2025-05-01 14:02:59,823 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for explore
2025-05-01 14:02:59,824 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for explore
2025-05-01 14:02:59,826 ERROR scheduler Skipped queueing hrms.controllers.employee_reminders.send_reminders_in_advance_monthly because it was found in queue for explore
2025-05-01 14:02:59,827 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for explore
2025-05-01 14:02:59,829 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for explore
2025-05-01 14:02:59,832 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for explore
2025-05-01 14:02:59,835 ERROR scheduler Skipped queueing erpnext.crm.doctype.email_campaign.email_campaign.send_email_to_leads_or_contacts because it was found in queue for explore
2025-05-01 14:02:59,837 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.visibility.visibility.trigger_daily_alerts because it was found in queue for explore
